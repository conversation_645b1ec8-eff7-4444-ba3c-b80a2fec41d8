<template>
    <view class="container">
        <view class="history" @click="showHistory">
            <text class="history-icon">&#8635;</text>
            <text class="history-text">历史记录</text>
        </view>
        <scroll-view class="scroll-area" scroll-y="true" :scroll-top="scrollTop" :scroll-with-animation="true"
            @scrolltolower="onScrollLower" show-scrollbar="false">
            <view class="list">
                <chat-item v-for="(item, index) in list" :key="index" :item="item" :image-urls="imageUrls"
                    @image-load="imageLoaded" @image-error="imageError"></chat-item>

                <!-- 加载中提示 -->
                <view class="loading-container" v-if="isStart">
                    <view class="loading-dots">
                        <view class="dot"></view>
                        <view class="dot"></view>
                        <view class="dot"></view>
                    </view>
                </view>
            </view>
            <!-- 底部占位 - 根据实际输入框高度和键盘高度动态调整 -->
            <view class="bottom-placeholder"></view>
        </scroll-view>



        <view class="bottom" :style="{
            bottom: 0,
            transform: isKeyboardShow ? 'translateY(0)' : 'translateY(0)',
        }" :class="{ 'keyboard-visible': isKeyboardShow }">
            <view class="input-container">
                <!-- 参考图片区域 -->
                <view class="reference-area-inline">
                    <view class="reference-btn-inline" v-if="referenceImages.length === 0"
                        @click="chooseReferenceImage">
                        <text class="reference-icon">+</text>
                    </view>
                    <view class="reference-item-inline" v-if="referenceImages" @click="chooseReferenceImage">
                        <image :src="referenceImages" class="reference-preview-inline" mode="aspectFill"></image>
                        <view class="reference-delete-inline" @click.stop="deleteReferenceImage(0)">×</view>
                        <view class="reference-overlay">
                            <text class="reference-overlay-text">重新选择</text>
                        </view>
                    </view>
                </view>
                <textarea v-model="content" class="r-input" placeholder="请输入提示词生成图片" placeholder-class="placeholder"
                    @confirm="send" @focus="inputFocus" @blur="inputBlur" :adjust-position="true" cursor-spacing="20"
                    auto-height :maxlength="-1" :style="{ height: textareaHeight, maxHeight: textareaMaxHeight }"
                    @input="adjustTextareaHeight"></textarea>
                <view class="clear-btn" v-if="content" @click="clearContent">
                    <text class="clear-icon">×</text>
                </view>
            </view>
            <view class="r-but" :class="{ 'r-but-active': content.trim() || referenceImages }" @click="send()">
                <text class="send-text">发送</text>
            </view>
        </view>
        <!-- 历史记录容器 - 带动画效果 -->
        <view class="history-mask" v-if="isShowHistory" @click="hideHistory"></view>
        <view class="history-container" v-if="isShowHistory" :class="{ 'history-container-active': isShowHistory }">
            <view class="history-header">
                <text class="history-title">历史记录</text>
                <view class="history-close" @click="hideHistory">×</view>
            </view>
            <view class="history-content">
                <view class="history-item" :class="{ 'history-item-active': session_id == item.dialogue_id }"
                    v-for="item in historyList" :key="item.dialogue_id" @click="selectHistoryItem(item)">
                    <view class="history-item-content">{{ item.first_content }}</view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import ChatItem from './components/chat-item/chat-item.vue';
import {
    decodedString
} from './utils/decodedString.js'
export default {
    components: {
        ChatItem
    },
    data() {
        return {
            video_title: '', //视频标题
            video_content: '', //视频内容
            isShowHistory: false,
            historyList: [],
            content: '',
            list: [],
            isStart: false,
            scrollTop: 0,
            safeAreaBottom: 0,
            keyboardHeight: 0, // 键盘高度
            isKeyboardShow: false, // 键盘是否显示
            // 保存所有AI生成的图片URL列表，用于多图预览
            imageUrls: [],
            platform: '',
            focusTimer: null,
            scrollTimer: null,
            windowHeight: 0, // 窗口高度
            contentHeight: 0, // 内容高度
            inputBarHeight: 120, // 输入框区域高度，单位rpx转换为px
            needScrollToBottom: true, // 是否需要滚动到底部
            isScrolling: false, // 是否正在滚动中
            lastScrollTime: 0, // 上次滚动时间
            scrollDebounceDelay: 200, // 滚动防抖延迟(ms)
            keyboardStateChangeTime: 0, // 键盘状态最后变化时间
            isAutoScrollPrevented: false, // 是否暂时阻止自动滚动
            keyboardTransitionDuration: 300, // 键盘过渡动画持续时间(ms)
            session_id: '', // 会话id
            cueWord: '', // 提示词
            type: null,
            requestTask: null,
            textareaHeight: '40rpx', // 文本域的初始高度
            textareaMinHeight: '40rpx', // 文本域的最小高度
            textareaMaxHeight: '300rpx', // 文本域的最大高度
            referenceImages: '', // 参考图片列表
            taskId: null, // 任务ID
            pollTimer: null, // 轮询定时器
        }
    },
    computed: {
        // 计算底部输入框的bottom值
        bottomPosition() {
            // 如果键盘显示，返回键盘高度，否则返回安全区域高度
            return this.isKeyboardShow ?
                this.keyboardHeight + 'px' :
                this.safeAreaBottom + 'px'
        },
        // 计算底部占位符高度
        bottomPlaceholderHeight() {
            // 根据不同状态计算底部占位符高度
            if (this.isKeyboardShow) {
                // 键盘显示时，考虑键盘高度 + 输入框高度
                const rpxToPx = this.windowHeight / 750 // 计算rpx到px的转换比例
                const inputHeightPx = this.inputBarHeight * rpxToPx // 将rpx转换为px

                // 键盘高度 + 输入框高度 + 额外间距
                return this.keyboardHeight + inputHeightPx + 20 + 'px'
            } else {
                // 键盘隐藏时，仅考虑输入框高度 + 安全区域
                return this.inputBarHeight + 20 + 'rpx'
            }
        },
    },
    watch: {
        // 监听键盘状态变化，更新UI
        isKeyboardShow(newVal, oldVal) {
            if (newVal !== oldVal) {
                // 键盘状态发生变化时，强制重新计算布局
                this.$nextTick(() => {
                    // 延迟执行，确保DOM已更新
                    setTimeout(() => {
                        this.forceLayout()
                        if (newVal) {
                            // 键盘弹出时，给页面元素足够时间适应新布局再滚动
                            setTimeout(() => {
                                if (this.isKeyboardShow) {
                                    // 再次检查，防止状态快速变化
                                    this.scrollToBottomDebounced()
                                }
                            }, this.keyboardTransitionDuration)
                        }
                    }, 300)
                })
            }
        },
        // 监听内容变化，自动滚动到底部，但增加防抖
        list: {
            handler(newVal, oldVal) {
                if (this.needScrollToBottom && newVal.length > 0) {
                    // 如果是添加了新消息
                    if (oldVal.length > 0 && newVal.length > oldVal.length) {
                        this.$nextTick(() => {
                            this.scrollToBottomDebounced()
                        })
                    }
                }
            },
            deep: true, // 深度监听
        },
    },
    onLoad(options) {
        if (options.type) {
            this.type = Number(options.type)
        }
        if (options.title) {
            uni.setNavigationBarTitle({
                title: options.title
            })
        }

        if (![9].includes(this.type)) {
            // 获取提示词
            this.getCueWord()
        }
        // if (uni.getStorageSync('AIImgList')) {
        // 	this.list = uni.getStorageSync('AIImgList')
        // }



        console.log(this.list);
        // 获取安全区域高度
        this.getSafeAreaBottom()
        // 监听键盘高度变化
        this.listenKeyboardHeight()
        // 获取窗口高度
        this.getWindowHeight()

    },
    onUnload() {
        // 页面卸载时移除键盘高度监听
        this.removeKeyboardListener()
        // 清理轮询定时器
        if (this.pollTimer) {
            clearTimeout(this.pollTimer);
            this.pollTimer = null;
        }
        // 中止流式请求
        if (this.requestTask) {
            this.requestTask.cancel();
            this.requestTask = null;
        }
        uni.setStorageSync('AIImgList', this.list)
    },
    onReady() {
        // 页面加载完成后，设置平台信息
        this.initPlatformInfo()


    },
    onShow() {
        // 页面显示时重新计算布局
        this.forceLayout()
    },
    mounted() {
        // 初始化时监听页面尺寸变化，以便检测键盘弹出
        uni.onWindowResize((res) => {
            console.log('窗口尺寸变化', res)
            // 记录键盘状态变化时间
            this.keyboardStateChangeTime = Date.now()

            // 计算窗口高度变化，判断键盘是否弹出
            const heightChange = this.windowHeight - res.size.windowHeight
            console.log('高度变化:', heightChange)

            if (heightChange > 100) {
                // 大幅度高度减小，键盘弹出
                console.log('键盘弹出')
                this.isKeyboardShow = true
                this.keyboardHeight = heightChange
            } else if (this.isKeyboardShow && heightChange < 50) {
                // 高度恢复，键盘收起
                console.log('键盘收起')
                this.isKeyboardShow = false
                this.keyboardHeight = 0

                // 键盘收起时，短暂阻止自动滚动
                this.isAutoScrollPrevented = true
                setTimeout(() => {
                    this.isAutoScrollPrevented = false
                }, this.keyboardTransitionDuration + 100)
            }
        })
    },
    methods: {
        // 选择参考图片
        chooseReferenceImage() {
            uni.chooseImage({
                count: 1, // 只能选择1张图片
                sizeType: ['compressed'],
                sourceType: ['album', 'camera'],
                success: (res) => {
                    // 上传图片到服务器
                    this.uploadReferenceImages(res.tempFilePaths);
                },
                fail: (err) => {
                    console.log('选择参考图片失败:', err);
                    this.$sun.toast('选择参考图片失败', 'none');
                }
            });
        },

        // 上传参考图片到服务器
        async uploadReferenceImages(tempFilePaths) {
            uni.showLoading({
                title: '上传中...'
            });

            try {
                const result = await this.uploadSingleReferenceImage(tempFilePaths[0]);
                if (result) {
                    // 替换现有图片
                    this.referenceImages = result;
                }
                uni.hideLoading();
                this.$sun.toast('参考图片上传成功', 'success');
            } catch (error) {
                uni.hideLoading();
                console.error('上传参考图片失败:', error);
                this.$sun.toast('参考图片上传失败', 'none');
            }
        },

        // 上传单张参考图片
        uploadSingleReferenceImage(filePath) {
            return new Promise((resolve, reject) => {
                uni.uploadFile({
                    url: this.$api.upload,
                    filePath: filePath,
                    name: 'file',
                    formData: {
                        uid: uni.getStorageSync('uid')
                    },
                    success: (res) => {
                        try {
                            const data = JSON.parse(res.data);
                            console.log(data);
                            if (data.errno === 0) {
                                resolve(data.data);
                            } else {
                                reject(new Error(data.message || '上传失败'));
                            }
                        } catch (e) {
                            reject(new Error('解析响应失败'));
                        }
                    },
                    fail: (err) => {
                        reject(err);
                    }
                });
            });
        },

        // 删除参考图片
        deleteReferenceImage(index) {
            this.referenceImages = '';
        },

        //前往克隆页
        getClone() {
            let compressed = 1;
            if (this.param.isSel == 4) {
                compressed = 2;
            }
            uni.navigateTo({
                url: '/pages/index/clone/clone?compressed=' + compressed
            })
        },
        //确认形象
        confirmImage(obj) {
            this.listObj = obj;

            this.param = {
                decode_img: this.listObj.video_cover,
                id: this.listObj.id,
                isSel: this.param.isSel,
                current_status: this.listObj.current_status,
                new_current_status: this.listObj.new_current_status,
                composite_current_status: this.listObj.composite_current_status,
                four_current_status: this.listObj.four_current_status
            };

            if (this.param.isSel == 3 || this.param.isSel == 2) {
                this.getImage(this.param.decode_img);
            }

            this.isPop = 2;

        },
        //我的形象
        async getAvatarList() {

            const result = await this.$http.post({
                url: this.$api.avatarList,
                data: {
                    uid: uni.getStorageSync('uid'),
                    buy_expire: 2, //资产市场购买 2未过期
                    current_status: this.param.isSel == 1 ? 'completed' : '',
                    new_current_status: this.param.isSel == 2 ? 'completed' : '',
                    composite_current_status: this.param.isSel == 3 ? 'completed' : '',
                    four_current_status: this.param.isSel == 4 ? 'completed' : '',
                    definition: this.param.isSel == 4 ? 2 : 1,
                    page: 1,
                    psize: 200
                }
            });
            if (result.errno == 0) {
                this.avatarList = result.data.list;
            }
        },
        getIsSel(type) {
            this.listObj = {
                id: '',
            };
            this.web_people_width = '';
            this.web_people_height = '';
            this.param = {
                decode_img: '',
                id: '',
                isSel: type,
            };
            this.getAvatarList();
        },
        closeBatch() {
            this.$refs.pop1.close();
        },
        async showHistory() {
            const result = await this.$http.post({
                url: this.$api.getAIConversationHistory,
                data: {
                    uid: uni.getStorageSync('uid'),
                    type: this.type,
                },
            })
            if (result.errno == 0) {
                this.historyList = result.data
                this.isShowHistory = true
                console.log(result.data);
            } else {
                this.$sun.toast(result.message, 'none')
            }
        },
        // 隐藏历史记录
        hideHistory() {
            this.isShowHistory = false
        },
        // 选择历史记录项
        async selectHistoryItem(item) {
            this.session_id = item.dialogue_id
            const result = await this.$http.post({
                url: this.$api.getOneDialogueHistory,
                data: {
                    uid: uni.getStorageSync('uid'),
                    type: this.type,
                    session_id: item.dialogue_id,
                },
            })
            if (result.errno == 0) {
                this.list = result.data
                this.hideHistory()
                this.scrollToBottom()
            } else {
                this.$sun.toast(result.message, 'none')
            }
        },
        // 初始化平台相关信息
        initPlatformInfo() {
            // 获取当前运行的平台
            // #ifdef APP-PLUS
            this.platform = 'app'
            // #endif

            // #ifdef H5
            this.platform = 'h5'
            // #endif

            // #ifdef MP-WEIXIN
            this.platform = 'mp-weixin'
            // #endif

            // 其他平台可以继续添加
            console.log('当前运行平台:', this.platform)
        },
        // 监听键盘高度变化 - 增强版
        listenKeyboardHeight() {
            // 使用uni-app提供的键盘高度变化监听API
            uni.onKeyboardHeightChange((res) => {
                console.log('键盘高度变化:', res.height)

                // 旧的键盘高度
                const oldKeyboardHeight = this.keyboardHeight

                // 设置键盘高度和显示状态
                this.keyboardHeight = res.height
                const wasKeyboardShow = this.isKeyboardShow
                this.isKeyboardShow = res.height > 0

                // 不同平台可能需要不同的处理方式
                if (this.platform === 'app') {
                    // App端可能需要特殊处理
                    // 某些安卓设备上可能需要额外的偏移量
                    if (uni.getSystemInfoSync().platform === 'android') {
                        // 安卓平台特殊处理
                    }
                }

                // 键盘弹出或高度变化时
                if (this.isKeyboardShow) {
                    // 如果是键盘初次弹出或键盘高度变化较大
                    if (
                        !wasKeyboardShow ||
                        Math.abs(oldKeyboardHeight - res.height) > 50
                    ) {
                        // 重新计算布局并滚动到底部
                        this.forceLayout()
                        this.scrollToBottomDebounced()
                    }
                } else if (wasKeyboardShow) {
                    // 键盘隐藏时，延迟一下再滚动，避免闪烁
                    setTimeout(() => {
                        this.forceLayout()
                        // 可选：键盘隐藏时也滚动到底部
                        // this.scrollToBottom();
                    }, 200)
                }
            })
        },
        // 移除键盘高度监听
        removeKeyboardListener() {
            // 使用uni-app提供的方法移除监听
            uni.offKeyboardHeightChange()
        },
        getSafeAreaBottom() {
            // 获取系统信息
            uni.getSystemInfo({
                success: (res) => {
                    // 计算底部安全区域
                    this.safeAreaBottom = res.safeAreaInsets ?
                        res.safeAreaInsets.bottom :
                        0
                    console.log('安全区域底部高度:', this.safeAreaBottom)
                },
            })
        },
        clearContent() {
            this.content = '';
            this.textareaHeight = this.textareaMinHeight;
            this.inputBarHeight = 120; // 重置为初始高度
        },
        // 获取窗口高度
        getWindowHeight() {
            const systemInfo = uni.getSystemInfoSync()
            this.windowHeight = systemInfo.windowHeight
            console.log('窗口高度:', this.windowHeight)
        },
        // 更新滚动区域高度
        updateContentHeight() {
            const query = uni.createSelectorQuery().in(this)
            query.select('.list').boundingClientRect()
            query.exec((res) => {
                if (res && res[0]) {
                    this.contentHeight = res[0].height
                    console.log('内容高度:', this.contentHeight)
                }
            })
        },
        // 带防抖功能的滚动到底部，增强键盘交互逻辑
        scrollToBottomDebounced() {
            // 如果键盘状态近期变化或暂时阻止自动滚动，可能需要额外延迟
            const now = Date.now()
            const timeSinceKeyboardChange = now - this.keyboardStateChangeTime

            // 如果键盘状态刚刚变化且变化时间太短，给更长的延迟
            if (timeSinceKeyboardChange < this.keyboardTransitionDuration) {
                console.log('键盘状态刚刚变化，延迟滚动')
                setTimeout(() => {
                    // 重新尝试滚动
                    this.scrollToBottomDebounced()
                }, this.keyboardTransitionDuration - timeSinceKeyboardChange + 50)
                return
            }

            // 如果暂时阻止自动滚动，则跳过
            if (this.isAutoScrollPrevented) {
                console.log('自动滚动暂时被阻止')
                return
            }

            // 如果已经在滚动中，或者距离上次滚动时间太短，则跳过
            if (
                this.isScrolling ||
                now - this.lastScrollTime < this.scrollDebounceDelay
            ) {
                console.log('跳过滚动，防抖中...')
                return
            }

            // 更新滚动状态和时间
            this.isScrolling = true
            this.lastScrollTime = now

            // 执行滚动
            this.scrollToBottom()

            // 一段时间后重置滚动状态
            setTimeout(() => {
                this.isScrolling = false
            }, this.scrollDebounceDelay)
        },
        // 完全重写的滚动到底部方法
        scrollToBottom() {
            // 清除之前的定时器
            clearTimeout(this.scrollTimer)

            // 单步滚动，没有先滚到顶部的步骤
            this.scrollTimer = setTimeout(() => {
                const query = uni.createSelectorQuery().in(this)
                query.select('.list').boundingClientRect()
                query.select('.scroll-area').boundingClientRect()
                query.exec((res) => {
                    if (res && res[0] && res[1]) {
                        // 列表高度
                        const listHeight = res[0].height
                        // 滚动区域高度
                        const scrollHeight = res[1].height

                        // 计算精确的滚动位置 - 列表实际高度减去一些偏移量
                        // 这比使用一个大倍数更稳定
                        const scrollPosition = Math.max(0, listHeight - scrollHeight + 100)

                        // console.log('精确滚动位置:', scrollPosition)

                        // 直接设置滚动位置，无需两步式滚动
                        this.scrollTop = scrollPosition
                    }
                })
            }, 100)
        },
        // 监听滚动事件，可以帮助调试
        onPageScroll(e) {
            // console.log('页面滚动:', e.scrollTop)
        },
        // 输入框获得焦点 - 增强键盘交互
        inputFocus(e) {
            console.log('输入框获得焦点', e)

            // 标记需要滚动到底部
            // this.needScrollToBottom = true
            // 记录键盘状态变化时间
            this.keyboardStateChangeTime = Date.now()

            // H5端可能需要特殊处理
            // #ifdef H5
            // 某些移动浏览器可能不会触发键盘高度变化事件
            clearTimeout(this.focusTimer)
            this.focusTimer = setTimeout(() => {
                if (!this.isKeyboardShow) {
                    // 如果键盘还没有被检测到弹出，手动设置键盘状态
                    this.isKeyboardShow = true
                    // 尝试使用一个估计的键盘高度
                    const estimatedKeyboardHeight = this.windowHeight * 0.4 // 大约是屏幕高度的40%
                    this.keyboardHeight = estimatedKeyboardHeight
                }

                // 为键盘动画留出足够时间，然后滚动
                setTimeout(() => {
                    if (this.isKeyboardShow) {
                        // 再次检查，防止状态快速变化
                        this.scrollToBottomDebounced()
                    }
                }, this.keyboardTransitionDuration)
            }, 300)
            // #endif

            // 其他平台，等待键盘高度变化事件触发后的滚动
        },
        // 输入框失去焦点 - 增强键盘交互
        inputBlur(e) {
            console.log('输入框失去焦点', e)

            // 记录键盘状态变化时间
            this.keyboardStateChangeTime = Date.now()

            // 键盘收起时，短暂阻止自动滚动，避免页面跳动
            this.isAutoScrollPrevented = true
            setTimeout(() => {
                this.isAutoScrollPrevented = false
            }, this.keyboardTransitionDuration + 100)

            // #ifdef H5
            // H5平台特殊处理
            clearTimeout(this.blurTimer)
            this.blurTimer = setTimeout(() => {
                // 如果键盘状态没有被系统事件更新，手动更新
                if (this.isKeyboardShow) {
                    this.isKeyboardShow = false
                    this.keyboardHeight = 0
                }

                // 强制重新计算布局
                this.forceLayout()
            }, 300)
            // #endif
        },
        // 获取提示词
        async getCueWord() {
            let data = {
                uid: uni.getStorageSync('uid'),
                type: this.type,
            }

            const result = await this.$http.post({
                url: this.$api.getCueWord,
                data: data,
            })
            if (result.errno == 0) {
                this.cueWord = result.data.content
                console.log(this.cueWord);
                this.session_id = result.data.session_id
                this.list.push({
                    role: "assistant",
                    content: this.cueWord,
                    id: Date.now(),
                })
            } else {
                this.$sun.toast(result.message, 'none')
            }
        },
        // 对话
        startStream() {
            this.list.push({
                role: "assistant",
                content: '',
                id: Date.now(),
            })
            let url = this.$api.taskStart;
            let data = {
                uid: uni.getStorageSync('uid'), // 请求参数
                content: this.content,
                session_id: this.session_id,
                type: this.type.toString(),
                img_url: this.referenceImages,
            }
            console.log('img_url 类型:', typeof data.img_url, '值:', data.img_url);
            // 使用流式请求获取任务ID
            this.requestTask = this.$http.requestStream(url, "POST", data, {
                onChunk: (data) => {
                    // 处理接收到的数据块
                    try {
                        // 检查数据是否为ArrayBuffer类型
                        console.log(data);
                        if (data instanceof ArrayBuffer) {
                            // 将ArrayBuffer转换为字符串
                            let text = '';
                            try {
                                // 使用解码函数处理ArrayBuffer
                                text = decodedString(data);
                                if (!text) {
                                    throw new Error("解码结果为空");
                                }
                            } catch (decodeError) {
                                console.error("解码ArrayBuffer失败:", decodeError);
                                // 尝试使用备用方法
                                const buffer = new Uint8Array(data);
                                text = String.fromCharCode.apply(null, buffer);
                            }
                            console.log('接收到的数据:', text);

                            // 尝试解析JSON获取任务ID
                            try {
                                const jsonData = JSON.parse(text);
                                if (jsonData && jsonData.data && jsonData.data.id) {
                                    console.log('获取到任务ID:', jsonData.data.id);
                                    // 保存任务ID
                                    this.taskId = jsonData.data.id;
                                    // 开始轮询获取结果
                                    this.pollTaskResult(jsonData.data.id);
                                }
                            } catch (parseError) {
                                console.log('解析任务ID失败:', parseError);
                            }

                            // 追加到聊天内容
                            this.list[this.list.length - 1].content += text;
                        } else if (typeof data === 'string') {
                            // 如果是字符串，直接追加到目标属性
                            console.log('接收到的字符串数据:', data);
                            this.list[this.list.length - 1].content += data;

                            // 尝试解析JSON获取任务ID
                            try {
                                const jsonData = JSON.parse(data);
                                if (jsonData && jsonData.data && jsonData.data.id) {
                                    console.log('获取到任务ID:', jsonData.data.id);
                                    // 保存任务ID
                                    this.taskId = jsonData.data.id;
                                    // 开始轮询获取结果
                                    this.pollTaskResult(jsonData.data.id);
                                }
                            } catch (parseError) {
                                console.log('解析任务ID失败:', parseError);
                            }
                        } else {
                            console.warn("收到未知类型的数据块:", data);
                        }

                        // 滚动到最新内容
                        this.scrollToBottom();
                    } catch (error) {
                        console.error("处理数据块出错:", error);
                    }
                },
                onComplete: () => {
                    console.log(`流式请求完成`);
                    this.isStart = false;
                },
                onError: (err) => {
                    uni.hideLoading();
                    this.isStart = false;
                    console.error(`流式请求错误:`, err);
                }
            });
        },
        // ai绘画
        async getAiImg(content) {
            this.isStart = true
            this.list.push({
                role: "assistant",
                content: '正在生成中...',
                id: Date.now() + 1, // 添加唯一ID
            })
            const result = await this.$http.post({
                url: this.$api.getGenerationsImg,
                data: {
                    content: content,
                    uid: uni.getStorageSync('uid'),
                    type: this.type,
                    session_id: this.session_id,
                },
                loading: false,
            })
            if (result.errno == 0) {
                this.list.pop()
                // 添加生成的图片到列表
                this.list.push({
                    role: "assistant",
                    img_url: result.data,
                    id: Date.now() + 1, // 添加唯一ID
                })

                // 将图片URL添加到图片URL列表中
                this.imageUrls.push(result.data)

                // 图片消息添加后滚动到底部，使用防抖版本
                this.$nextTick(() => {
                    this.scrollToBottomDebounced()
                })
            } else {
                this.$sun.toast(result.message, 'none')
                this.list.pop()
                this.list.push({
                    role: "assistant",
                    content: '生成失败，请重新生成',
                    id: Date.now() + 1, // 添加唯一ID
                })
            }
            this.isStart = false
        },
        // 中止请求
        stopStream() {
            if (this.requestTask) {
                this.requestTask.cancel();
                this.requestTask = null;
                console.log('已中止请求');
            }
        },
        // 发送消息
        async send() {



            if (this.isStart) {
                this.$sun.toast('正在生成请稍后', 'none')
                return
            } else {
                this.isStart = true
            }

            // 验证内容不为空或type===17时有参考图片
            if (!this.content.trim() && this.referenceImages) {
                this.$sun.toast('请输入内容或添加参考图片', 'none')
                this.isStart = false
                return
            }

            // 设置键盘状态
            const wasKeyboardShow = this.isKeyboardShow

            this.list.push({
                role: "user",
                content: this.content,
                id: Date.now(),
            })

            // 如果是type===17且有参考图片，添加图片到消息中
            if (this.referenceImages) {
                this.list[this.list.length - 1].reference_images = [this.referenceImages];
            }
            this.scrollToBottom()

            // 发送消息后，确保足够延迟以适应可能的键盘状态变化
            this.$nextTick(() => {
                // 增加发送后延迟，以适应可能的键盘收起动画
                setTimeout(() => {
                    if (!this.isKeyboardShow && wasKeyboardShow) {
                        // 如果键盘刚刚收起，给额外时间
                        setTimeout(() => {
                            this.scrollToBottomDebounced()
                        }, this.keyboardTransitionDuration)
                    } else {
                        this.scrollToBottomDebounced()
                    }
                }, 100)
            })

            // if ([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15].includes(this.type)) {
            this.startStream()
            // }
            // else {
            // 	this.getAiImg()
            // }


        },

        imageLoaded(e) {
            console.log('图片加载成功')

            // 图片加载完成后滚动到底部，使用防抖版本
            this.$nextTick(() => {
                this.scrollToBottomDebounced()
            })
        },

        imageError(e) {
            // 图片加载失败的处理
            console.log('图片加载失败')
            this.$sun.toast('图片加载失败', 'none')
        },

        // 滚动到底部事件
        onScrollLower() {
            // console.log('已滚动到底部')
        },
        // 强制重新计算布局
        forceLayout() {
            // 更新内容高度
            this.updateContentHeight()
            // 获取窗口高度
            this.getWindowHeight()
        },
        // 添加新方法：调整文本域高度
        adjustTextareaHeight(e) {
            // 根据内容自动调整高度，但不超过最大高度
            // auto-height 属性会自动处理高度，这里只需要确保不超过最大高度
            const query = uni.createSelectorQuery().in(this);
            query.select('.r-input').boundingClientRect(rect => {
                if (rect) {
                    let height = rect.height + 'px';
                    // 将px转换为rpx
                    const pxToRpx = 750 / uni.getSystemInfoSync().windowWidth;
                    // 提取最大高度数值（去掉'rpx'）
                    const maxHeightValue = parseInt(this.textareaMaxHeight);
                    const heightInRpx = Math.min(parseInt(rect.height * pxToRpx), maxHeightValue) + 'rpx';
                    this.textareaHeight = heightInRpx;

                    // 更新输入栏高度，影响底部占位符计算
                    this.inputBarHeight = Math.max(120, parseInt(rect.height * pxToRpx) + 40); // 40rpx为内边距

                    // 如果需要，可以在高度变化后触发滚动到底部
                    this.scrollToBottomDebounced();
                }
            }).exec();
        },
        // 解析设计师JSON数据
        parseDesignerJson(text) {
            try {
                // 首先尝试直接将整个文本解析为JSON
                let jsonData = JSON.parse(text);

                // 如果解析成功，检查是否有content字段
                if (jsonData && jsonData.content) {
                    console.log('设计师数据解析成功，content值:', jsonData.content);
                    // 可以在这里添加提示信息，如：正在生成科技感logo图片！
                    // this.$sun.toast('正在生成科技感logo图片！', 'none');
                } else {
                    console.log('设计师数据解析成功，但没有content字段:', jsonData);
                }
            } catch (error) {
                // 如果直接解析失败，尝试从文本中提取JSON部分
                try {
                    // 尝试用正则表达式匹配JSON格式的内容
                    const jsonRegex = /\{[\s\S]*?\}/;
                    const match = text.match(jsonRegex);

                    if (match && match[0]) {
                        // 尝试解析提取出的JSON字符串
                        let jsonData = JSON.parse(match[0]);

                        if (jsonData && jsonData.content) {
                            console.log('从文本中提取并解析设计师数据成功，content值:', jsonData.content);
                            // this.$sun.toast('正在生成科技感logo图片！', 'none');
                            this.getAiImg(jsonData.content);
                        } else {
                            console.log('从文本中提取并解析设计师数据成功，但没有content字段:', jsonData);
                        }
                    } else {
                        console.error('未找到有效的JSON数据');
                    }
                } catch (extractError) {
                    console.error('解析设计师数据失败:', extractError);
                }
            }
        },
        // 添加新方法：解析视频JSON数据
        parseVideoJson(text) {
            try {
                // 尝试从文本中提取JSON部分
                const jsonRegex = /```json\s*([\s\S]*?)\s*```/;
                const match = text.match(jsonRegex);

                if (match && match[1]) {
                    // 解析JSON字符串
                    const jsonData = JSON.parse(match[1]);

                    // 提取title和content并赋值
                    if (jsonData.title) {
                        this.video_title = jsonData.title;
                    }

                    if (jsonData.content) {
                        this.video_content = jsonData.content;
                    }

                    uni.navigateTo({
                        url: '/pages/index/clip/clip?title=' + this.video_title + '&content=' + this
                            .video_content
                    })

                    console.log('视频数据解析成功:', this.video_title, this.video_content);
                } else {
                    console.error('未找到有效的JSON数据');
                }
            } catch (error) {
                console.error('解析JSON数据失败:', error);
            }
        },
        // 轮询获取任务结果
        async pollTaskResult(taskId) {
            console.log('开始轮询任务结果，任务ID:', taskId);

            // 清除之前的定时器
            if (this.pollTimer) {
                clearTimeout(this.pollTimer);
            }

            const pollResult = async () => {
                try {
                    const result = await this.$http.post({
                        url: this.$api.taskEnd,
                        data: {
                            task_id: taskId,
                        }
                    });

                    console.log('轮询结果:', result);
                    console.log(result.data.content);
                    if (result.errno === 0) {
                        // 任务完成
                        if (result.data.content) {
                            console.log('任务完成，结果:', result.data.content.video_url);

                            // 清空参考图片
                            this.referenceImages = '';
                            this.content = '';
                            // 处理结果数据
                            if (result.data.content && result.data.content.video_url) {
                                // 设置视频URL
                                this.list[this.list.length - 1].video_url = result.data.content.video_url;
                                // 将视频URL添加到图片URL列表中（用于预览）
                                this.imageUrls.push(result.data.content.video_url);
                                // 清空content，因为这是视频消息
                                this.list[this.list.length - 1].content = '';
                                console.log('视频URL已设置:', result.data.content.video_url);
                            }

                            // 停止轮询
                            this.isStart = false;
                            return;
                        } else if (result.data && result.data.status === 'failed') {
                            // 任务失败
                            console.log('任务失败:', result.data);
                            this.list[this.list.length - 1].content = '任务执行失败，请重试';
                            this.isStart = false;
                            return;
                        } else {
                            // 任务仍在进行中，继续轮询
                            console.log('任务进行中，继续轮询...');
                            this.list[this.list.length - 1].content = '正在处理中...';
                        }
                    } else {
                        // API调用失败
                        console.error('轮询API调用失败:', result.message);
                        this.list[this.list.length - 1].content = '获取结果失败: ' + result.message;
                        this.isStart = false;
                        return;
                    }

                    // 继续轮询，3秒后再次请求
                    this.pollTimer = setTimeout(pollResult, 3000);

                } catch (error) {
                    console.error('轮询出错:', error);
                    this.list[this.list.length - 1].content = '轮询出错，请重试';
                    this.isStart = false;
                }
            };

            // 开始轮询
            pollResult();
        },
    },
}
</script>

<style lang="scss">
.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    position: relative;
    background-color: #080e1e;
    overflow: hidden; // 防止内容溢出
}

.history {
    position: fixed;
    top: 20rpx;
    right: 0;
    padding: 12rpx 24rpx 12rpx 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(90deg, rgba(68, 65, 253, 0.85), rgba(105, 229, 253, 0.85));
    border-radius: 30rpx 0 0 30rpx;
    color: #fff;
    font-size: 28rpx;
    z-index: 10;
    box-shadow: 0 4rpx 15rpx rgba(68, 65, 253, 0.3),
        inset 0 1rpx 3rpx rgba(255, 255, 255, 0.4);
    transition: all 0.3s ease;
    border: 1rpx solid rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(5rpx);

    &:active {
        transform: translateX(-5rpx) scale(0.98);
        box-shadow: 0 2rpx 8rpx rgba(68, 65, 253, 0.2),
            inset 0 1rpx 2rpx rgba(255, 255, 255, 0.3);
    }
}

.history-icon {
    margin-right: 10rpx;
    font-size: 36rpx;
    font-weight: bold;
    text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2);
}

.history-text {
    font-weight: 500;
    letter-spacing: 1rpx;
}

.history-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 98;
}

.history-container {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: 70%;
    background-color: #192236;
    z-index: 99;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    display: flex;
    flex-direction: column;
    box-shadow: 2rpx 0 10rpx rgba(0, 0, 0, 0.3);
}

.history-item-active {
    background-color: #080e1e;
    color: #fff;
}

.history-container-active {
    transform: translateX(0);
}

.history-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx;
    border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

.history-title {
    color: #fff;
    font-size: 32rpx;
    font-weight: bold;
}

.history-close {
    color: #fff;
    font-size: 40rpx;
}

// 参考图片区域样式 - 输入框内嵌版本
.reference-area-inline {
    display: flex;
    align-items: center;
    gap: 15rpx;
    margin-bottom: 15rpx;
}

.reference-btn-inline {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20rpx;
    width: 70rpx;
    height: 70rpx;
    background-color: #4a4a4a;
    border-radius: 12rpx;
    color: #fff;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
    flex-shrink: 0;

    &:active {
        transform: scale(0.95);
        background-color: #5a5a5a;
    }
}

.reference-icon {
    font-size: 32rpx;
    font-weight: bold;
    color: #fff;
}

.reference-images-inline {
    display: flex;
    align-items: center;
    gap: 12rpx;
}

.reference-item-inline {
    position: relative;
    margin-right: 20rpx;
    width: 70rpx;
    height: 70rpx;
    border-radius: 12rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
    background-color: #2a2a2a;
    flex-shrink: 0;
    cursor: pointer;
    transition: all 0.3s ease;

    &:active {
        transform: scale(0.95);
    }
}

.reference-preview-inline {
    width: 100%;
    height: 100%;
    border-radius: 12rpx;
    object-fit: cover;
}

.reference-delete-inline {
    position: absolute;
    top: -6rpx;
    right: -6rpx;
    width: 24rpx;
    height: 24rpx;
    background: rgba(255, 0, 0, 0.8);
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18rpx;
    font-weight: bold;
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.3);
    z-index: 10;

    &:active {
        transform: scale(0.9);
    }
}

.reference-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12rpx;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.reference-item-inline:hover .reference-overlay,
.reference-item-inline:active .reference-overlay {
    opacity: 1;
}

.reference-overlay-text {
    color: #fff;
    font-size: 20rpx;
    font-weight: 500;
    text-align: center;
}

.history-content {
    flex: 1;
    overflow-y: auto;
    padding: 10rpx 0;
}

.history-item {
    padding: 15rpx 20rpx;
    border-bottom: 1rpx solid rgba(255, 255, 255, 0.05);
    cursor: pointer;
}

.history-item:active {
    background-color: rgba(255, 255, 255, 0.05);
}

.history-item-content {
    color: #fff;
    font-size: 28rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.scroll-area {
    flex: 1;
    height: 100%;
    position: relative;
    /* 确保滚动区域正确显示，防止被输入框覆盖 */
    padding-bottom: env(safe-area-inset-bottom);
}

.list {
    padding: 20rpx 30rpx;
    padding-bottom: 40rpx;
    /* 设置内容最小高度，确保短内容时也能正常滚动 */
    min-height: 60vh;
    margin-top: 90rpx;
}

/* 确保全局类仍然存在 */
.in {
    justify-content: flex-end !important;
}

.out {
    justify-content: flex-start !important;
}

.loading-container {
    display: flex;
    justify-content: center;
    margin: 20rpx 0;

    .loading-dots {
        display: flex;
        align-items: center;

        .dot {
            width: 16rpx;
            height: 16rpx;
            background: rgba(99, 102, 241, 0.6);
            border-radius: 50%;
            margin: 0 6rpx;
            animation: dotPulse 1.5s infinite ease-in-out;

            &:nth-child(2) {
                animation-delay: 0.2s;
            }

            &:nth-child(3) {
                animation-delay: 0.4s;
            }
        }
    }
}

.bottom-placeholder {
    transition: height 0.25s ease; // 添加平滑过渡效果
    min-height: 120rpx; // 确保有最小高度
}

@keyframes dotPulse {

    0%,
    100% {
        transform: scale(0.6);
        opacity: 0.6;
    }

    50% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10rpx);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }

    to {
        opacity: 1;
        transform: scale(1);
    }
}

.bottom {
    position: fixed;
    right: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 30rpx;
    background-color: rgba(8, 14, 30, 0.95);
    backdrop-filter: blur(10rpx);
    border-top: 2rpx solid rgba(255, 255, 255, 0.05);
    z-index: 10;
    transition: bottom 0.25s ease, transform 0.25s ease; // 添加平滑过渡效果
    will-change: transform, bottom; // 优化性能

    &.keyboard-visible {
        background-color: rgba(8, 14, 30, 1); // 键盘显示时底色更深
    }
}

.input-container {
    position: relative;
    flex: 1;
    margin-right: 20rpx;
    display: flex;
    align-items: center;
}

.r-input {
    // width: 460rpx;
    padding: 20rpx 60rpx 20rpx 30rpx;
    background-color: rgba(25, 34, 54, 0.8);
    border-radius: 40rpx;
    color: #fff;
    min-height: 40rpx;
    font-size: 28rpx;
    transition: all 0.3s ease;
    border: 2rpx solid rgba(99, 102, 241, 0.3);
    line-height: 40rpx; // 确保文字垂直居中
    box-sizing: border-box;
    overflow-y: auto; // 当内容超出最大高度时允许滚动

    &:focus {
        border-color: rgba(99, 102, 241, 0.7);
        background-color: rgba(30, 41, 59, 0.8);
        box-shadow: 0 0 15rpx rgba(99, 102, 241, 0.3);
    }
}

.clear-btn {
    position: absolute;
    right: 20rpx;
    top: 50%;
    transform: translateY(-50%);
    width: 40rpx;
    height: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
}

.clear-icon {
    color: rgba(255, 255, 255, 0.6);
    font-size: 40rpx;
    font-weight: bold;
}

.r-but {
    font-size: 28rpx;
    color: #fff;
    height: 80rpx;
    line-height: 80rpx;
    width: 120rpx;
    text-align: center;
    border-radius: 40rpx;
    box-shadow: 2rpx 4rpx 16rpx 0 rgba(30, 156, 214, 0.4),
        inset 0 0 12rpx 0 rgba(204, 235, 255, 0.2);
    background: linear-gradient(90deg,
            rgba(105, 229, 253, 0.7),
            rgba(68, 65, 253, 0.7) 100%);
    transition: all 0.3s ease;
    opacity: 0.7;

    &-active {
        opacity: 1;
        box-shadow: 4rpx 6rpx 28rpx 0 rgba(30, 156, 214, 0.6),
            inset 0 0 22rpx 0 rgba(204, 235, 255, 0.3);
        background: linear-gradient(90deg,
                rgb(105, 229, 253),
                rgb(68, 65, 253) 100%);
    }

    &:active {
        transform: scale(0.95);
    }

    .send-text {
        font-weight: 500;
    }
}

.placeholder {
    color: rgba(153, 153, 153, 0.7);
    font-size: 28rpx;
}

page {
    border-top: none;
    background-color: #080e1e;
    overflow-x: hidden;
}

// 预览图片过渡动画
@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }

    to {
        opacity: 1;
        transform: scale(1);
    }
}

@media (max-height: 600px) {

    /* 小屏幕设备额外调整 */
    .bottom-placeholder {
        min-height: 100rpx;
    }

    .r-input {
        height: 36rpx; // 这里仅设置初始高度，不覆盖max-height
        padding: 16rpx 60rpx 16rpx 30rpx;
    }

    .r-but {
        height: 70rpx;
        line-height: 70rpx;
    }
}
</style>