# 收藏功能测试说明

## 功能概述
为chat-item组件添加了收藏功能，支持收藏AI回复的图片、视频和文本内容。

## 新增Props
- `showFavorite`: Boolean，控制是否显示收藏按钮，默认为false
- `sessionType`: String/Number，当前会话类型，用于收藏时的分类

## 收藏按钮位置
1. **图片容器**: 在放大提示旁边显示收藏按钮
2. **视频容器**: 在播放提示旁边显示收藏按钮  
3. **文本容器**: 在复制按钮旁边显示收藏按钮

## 使用方法
```vue
<chat-item 
  :item="chatItem" 
  :show-favorite="true" 
  :session-type="currentSessionType"
  @favorite="handleFavorite"
  @favorite-error="handleFavoriteError"
/>
```

## 事件处理
- `favorite`: 收藏成功时触发，传递收藏数据
- `favorite-error`: 收藏失败时触发，传递错误信息

## 收藏数据结构
```javascript
{
  item: Object,           // 原始聊天项数据
  contentType: String,    // 'image', 'video', 'text'
  sessionType: String,    // 会话类型
  timestamp: Number,      // 收藏时间戳
  url: String,           // 图片/视频URL (仅图片和视频)
  content: String,       // 原始内容 (仅文本)
  plainText: String      // 纯文本内容 (仅文本)
}
```

## 样式特点
- 收藏按钮样式与复制按钮保持一致
- 使用心形图标表示收藏功能
- 支持hover和点击效果
- 在图片/视频容器中与提示按钮并排显示

## 测试步骤
1. 在使用chat-item的页面中传入`show-favorite="true"`
2. 传入当前会话类型`session-type`
3. 监听`favorite`事件处理收藏逻辑
4. 测试不同内容类型的收藏功能
